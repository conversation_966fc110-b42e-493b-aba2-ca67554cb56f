using System;
using System.Text;
using System.Windows.Forms;
using Script.Methods;
/************************************
Shell Module default code: using .NET Framwwork 4.6.1
*************************************/
public partial class UserScript:ScriptMethods,IProcessMethods
{
    //the count of process
	//执行次数计数
    int processCount ;  

    /// <summary>
    /// Initialize the field's value when compiling
	/// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
		//变量初始化，其余变量可在该函数中添加
        processCount = 0;
       
    }

    /// <summary>
    /// Enter the process function when running code once
	/// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>
    public bool Process()
    {
        // 解析钩外径算法参数为数组
        string[] dangleArray = !string.IsNullOrEmpty(dangle) ? dangle.Split(';') : new string[0];
        string[] disArray = !string.IsNullOrEmpty(dis) ? dis.Split(';') : new string[0];
        
        // 特征值匹配逻辑（此处实现简单长度匹配示例，可根据实际需求修改）
        bool isMatched = dangleArray.Length > 0 && disArray.Length > 0 && dangleArray.Length == disArray.Length;
        
        if (isMatched && pic != null)
        {
            try
            {
                // 创建基于日期时间的文件夹路径
                string basePath = System.IO.Path.Combine(Application.StartupPath, "MatchedImages");
                string timeStamp = DateTime.Now.ToString("yyyyMMdd_HHmmss_fff");
                string saveDir = System.IO.Path.Combine(basePath, timeStamp);
                System.IO.Directory.CreateDirectory(saveDir);
                
                // 保存图像到本地（需根据VisionMaster的IMAGE类型调整保存方法）
                string imagePath = System.IO.Path.Combine(saveDir, "matched_image.bmp");
                // 假设VMOperator提供图像保存方法，实际使用时请替换为正确的API
                VMOperator.SaveImage(pic, imagePath);
                
                // 记录匹配日志
                string logPath = System.IO.Path.Combine(saveDir, "match_info.txt");
                string logContent = string.Format("匹配时间: {0}\n特征值数量: {1}\ndangle: {2}\ndis: {3}",
                    DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    dangleArray.Length,
                    dangle,
                    dis);
                System.IO.File.WriteAllText(logPath, logContent, System.Text.Encoding.UTF8);
            }
            catch (Exception ex)
            {
                MessageBox.Show("图像保存失败: " + ex.Message);
            }
        }
        
        return true;
    }
}
                            